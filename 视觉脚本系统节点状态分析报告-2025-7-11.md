# 视觉脚本系统节点状态分析报告
## 分析日期：2025-7-11

## 📊 统计概览

- **引擎注册节点总数**：169 个
- **编辑器集成节点总数**：366 个
- **双重注册节点数**：134 个
- **仅引擎注册节点数**：39 个
- **仅编辑器集成节点数**：232 个

## 📋 完整节点清单

### 引擎注册节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 源文件 |
|------|----------|------------|--------------|----------------|--------|
| 001 | core/events/onStart | 开始 | 是 | 是 | CoreNodes.ts |
| 002 | core/events/onUpdate | 更新 | 是 | 是 | CoreNodes.ts |
| 003 | core/events/onEnd | 结束 | 是 | 是 | CoreNodes.ts |
| 004 | core/events/onPause | 暂停 | 是 | 是 | CoreNodes.ts |
| 005 | core/events/onResume | 恢复 | 是 | 是 | CoreNodes.ts |
| 006 | core/flow/branch | 分支 | 是 | 是 | CoreNodes.ts |
| 007 | core/flow/sequence | 序列 | 是 | 是 | CoreNodes.ts |
| 008 | core/debug/print | 打印日志 | 是 | 是 | CoreNodes.ts |
| 009 | core/flow/delay | 延时 | 是 | 是 | CoreNodes.ts |
| 010 | logic/flow/branch | 分支 | 是 | 否 | LogicNodes.ts |
| 011 | logic/comparison/equal | 相等 | 是 | 是 | LogicNodes.ts |
| 012 | logic/comparison/notEqual | 不相等 | 是 | 是 | LogicNodes.ts |
| 013 | logic/comparison/greater | 大于 | 是 | 是 | LogicNodes.ts |
| 014 | logic/comparison/greaterEqual | 大于等于 | 是 | 否 | LogicNodes.ts |
| 015 | logic/comparison/less | 小于 | 是 | 是 | LogicNodes.ts |
| 016 | logic/comparison/lessEqual | 小于等于 | 是 | 否 | LogicNodes.ts |
| 017 | logic/operation/and | 与 | 是 | 否 | LogicNodes.ts |
| 018 | logic/operation/or | 或 | 是 | 否 | LogicNodes.ts |
| 019 | logic/operation/not | 非 | 是 | 否 | LogicNodes.ts |
| 020 | logic/flow/toggle | 开关 | 是 | 否 | LogicNodes.ts |
| 021 | math/basic/add | 加法 | 是 | 是 | MathNodes.ts |
| 022 | math/basic/subtract | 减法 | 是 | 是 | MathNodes.ts |
| 023 | math/basic/multiply | 乘法 | 是 | 是 | MathNodes.ts |
| 024 | math/basic/divide | 除法 | 是 | 是 | MathNodes.ts |
| 025 | math/basic/modulo | 取模 | 是 | 是 | MathNodes.ts |
| 026 | math/advanced/power | 幂运算 | 是 | 否 | MathNodes.ts |
| 027 | math/advanced/sqrt | 平方根 | 是 | 否 | MathNodes.ts |
| 028 | math/trigonometric/sin | 正弦 | 是 | 否 | MathNodes.ts |
| 029 | math/trigonometric/cos | 余弦 | 是 | 否 | MathNodes.ts |
| 030 | math/trigonometric/tan | 正切 | 是 | 否 | MathNodes.ts |
| 031 | entity/create | 创建实体 | 是 | 是 | EntityNodes.ts |
| 032 | entity/destroy | 销毁实体 | 是 | 是 | EntityNodes.ts |
| 033 | entity/find | 查找实体 | 是 | 是 | EntityNodes.ts |
| 034 | entity/get | 获取实体 | 是 | 是 | EntityNodes.ts |
| 035 | entity/component/get | 获取组件 | 是 | 是 | EntityNodes.ts |
| 036 | entity/component/add | 添加组件 | 是 | 是 | EntityNodes.ts |
| 037 | entity/component/remove | 移除组件 | 是 | 是 | EntityNodes.ts |
| 038 | entity/component/has | 检查组件 | 是 | 否 | EntityNodes.ts |
| 039 | physics/collision/onCollisionExit | 碰撞结束事件 | 是 | 是 | PhysicsNodes.ts |
| 040 | physics/collision/onTriggerEnter | 触发器进入事件 | 是 | 是 | PhysicsNodes.ts |
| 041 | physics/collision/onTriggerExit | 触发器退出事件 | 是 | 是 | PhysicsNodes.ts |
| 042 | physics/world/setGravity | 设置重力 | 是 | 是 | PhysicsNodes.ts |
| 043 | physics/world/setTimeStep | 设置时间步长 | 是 | 是 | PhysicsNodes.ts |
| 044 | physics/character/createCharacterController | 创建角色控制器 | 是 | 是 | PhysicsNodes.ts |
| 045 | physics/character/moveCharacter | 移动角色 | 是 | 是 | PhysicsNodes.ts |
| 046 | physics/character/jumpCharacter | 角色跳跃 | 是 | 是 | PhysicsNodes.ts |
| 047 | physics/vehicle/createVehicle | 创建载具 | 是 | 是 | PhysicsNodes.ts |
| 048 | physics/vehicle/setEngineForce | 设置引擎力 | 是 | 是 | PhysicsNodes.ts |
| 049 | physics/vehicle/setBrakeForce | 设置制动力 | 是 | 是 | PhysicsNodes.ts |
| 050 | physics/vehicle/setSteeringValue | 设置转向值 | 是 | 是 | PhysicsNodes.ts |
| 051 | physics/fluid/createFluidSimulation | 创建流体模拟 | 是 | 是 | PhysicsNodes.ts |
| 052 | physics/cloth/createClothSimulation | 创建布料模拟 | 是 | 是 | PhysicsNodes.ts |
| 053 | physics/destruction/createDestructible | 创建可破坏物体 | 是 | 是 | PhysicsNodes.ts |
| 054 | physics/raycast | 射线检测 | 是 | 是 | PhysicsNodes.ts |
| 055 | physics/applyForce | 应用力 | 是 | 是 | PhysicsNodes.ts |
| 056 | physics/collisionDetection | 碰撞检测 | 是 | 否 | PhysicsNodes.ts |
| 057 | physics/createConstraint | 创建约束 | 是 | 否 | PhysicsNodes.ts |
| 058 | physics/createMaterial | 创建物理材质 | 是 | 否 | PhysicsNodes.ts |
| 059 | physics/velocity/set | 设置速度 | 是 | 是 | PhysicsNodes.ts |
| 060 | physics/softbody/createCloth | 创建布料 | 是 | 是 | SoftBodyNodes.ts |
| 061 | physics/softbody/createRope | 创建绳索 | 是 | 是 | SoftBodyNodes.ts |
| 062 | physics/softbody/createBalloon | 创建气球 | 是 | 否 | SoftBodyNodes.ts |
| 063 | physics/softbody/createJelly | 创建果冻 | 是 | 否 | SoftBodyNodes.ts |
| 064 | physics/softbody/cut | 切割软体 | 是 | 否 | SoftBodyNodes.ts |
| 065 | animation/legacy/playAnimation | 播放动画 | 是 | 否 | AnimationNodes.ts |
| 066 | animation/legacy/stopAnimation | 停止动画 | 是 | 否 | AnimationNodes.ts |
| 067 | animation/legacy/setAnimationSpeed | 设置动画速度 | 是 | 否 | AnimationNodes.ts |
| 068 | animation/legacy/getAnimationState | 获取动画状态 | 是 | 否 | AnimationNodes.ts |
| 069 | animation/clip/createAnimationClip | 创建动画片段 | 是 | 是 | AnimationNodes.ts |
| 070 | animation/clip/addKeyframe | 添加关键帧 | 是 | 是 | AnimationNodes.ts |
| 071 | animation/clip/setInterpolation | 设置插值方式 | 是 | 是 | AnimationNodes.ts |
| 072 | animation/mixer/createAnimationMixer | 创建动画混合器 | 是 | 是 | AnimationNodes.ts |
| 073 | animation/mixer/playAnimationAction | 播放动画动作 | 是 | 否 | AnimationNodes.ts |
| 074 | animation/skeleton/createSkeletalAnimation | 创建骨骼动画 | 是 | 否 | AnimationNodes.ts |
| 075 | animation/skeleton/setBoneTransform | 设置骨骼变换 | 是 | 否 | AnimationNodes.ts |
| 076 | animation/ik/createIKConstraint | 创建IK约束 | 是 | 否 | AnimationNodes.ts |
| 077 | animation/ik/solveIK | 解算IK | 是 | 是 | AnimationNodes.ts |
| 078 | animation/morph/createMorphTarget | 创建变形目标 | 是 | 是 | AnimationNodes.ts |
| 079 | animation/morph/setMorphWeight | 设置变形权重 | 是 | 是 | AnimationNodes.ts |
| 080 | animation/curve/createAnimationCurve | 创建动画曲线 | 是 | 是 | AnimationNodes.ts |
| 081 | animation/curve/evaluateCurve | 评估曲线 | 是 | 是 | AnimationNodes.ts |
| 082 | animation/statemachine/createStateMachine | 创建状态机 | 是 | 否 | AnimationNodes.ts |
| 083 | animation/statemachine/transitionState | 状态转换 | 是 | 否 | AnimationNodes.ts |
| 084 | network/connectToServer | 连接到服务器 | 是 | 是 | NetworkNodes.ts |
| 085 | network/sendMessage | 发送网络消息 | 是 | 是 | NetworkNodes.ts |
| 086 | network/events/onMessage | 接收网络消息 | 是 | 是 | NetworkNodes.ts |
| 087 | ai/animation/generateBodyAnimation | 生成身体动画 | 是 | 是 | AINodes.ts |
| 088 | ai/animation/generateFacialAnimation | 生成面部动画 | 是 | 是 | AINodes.ts |
| 089 | debug/breakpoint | 断点 | 是 | 是 | DebugNodes.ts |
| 090 | debug/log | 日志 | 是 | 是 | DebugNodes.ts |
| 091 | debug/performanceTimer | 性能计时 | 是 | 是 | DebugNodes.ts |
| 092 | debug/variableWatch | 变量监视 | 是 | 是 | DebugNodes.ts |
| 093 | debug/assert | 断言 | 是 | 是 | DebugNodes.ts |
| 094 | network/security/encryptData | 数据加密 | 是 | 是 | NetworkSecurityNodes.ts |
| 095 | network/security/decryptData | 数据解密 | 是 | 是 | NetworkSecurityNodes.ts |
| 096 | network/security/computeHash | 计算哈希 | 是 | 否 | NetworkSecurityNodes.ts |
| 097 | network/security/generateSignature | 生成签名 | 是 | 否 | NetworkSecurityNodes.ts |
| 098 | network/security/verifySignature | 验证签名 | 是 | 否 | NetworkSecurityNodes.ts |
| 099 | network/security/createSession | 创建会话 | 是 | 否 | NetworkSecurityNodes.ts |
| 100 | network/security/validateSession | 验证会话 | 是 | 是 | NetworkSecurityNodes.ts |
| 101 | network/security/authenticateUser | 用户认证 | 是 | 是 | NetworkSecurityNodes.ts |
| 102 | network/webrtc/createConnection | 创建WebRTC连接 | 是 | 是 | WebRTCNodes.ts |
| 103 | network/webrtc/sendDataChannelMessage | 发送数据通道消息 | 是 | 是 | WebRTCNodes.ts |
| 104 | network/webrtc/onDataChannelMessage | 数据通道消息事件 | 是 | 否 | WebRTCNodes.ts |
| 105 | ai/emotion/analyze | 情感分析 | 是 | 是 | AIEmotionNodes.ts |
| 106 | ai/emotion/driveAnimation | 情感驱动动画 | 是 | 是 | AIEmotionNodes.ts |
| 107 | ai/nlp/classifyText | 文本分类 | 是 | 是 | AINLPNodes.ts |
| 108 | ai/nlp/recognizeEntities | 命名实体识别 | 是 | 是 | AINLPNodes.ts |
| 109 | ai/nlp/generateSummary | 生成文本摘要 | 是 | 否 | AINLPNodes.ts |
| 110 | ai/nlp/translateText | 语言翻译 | 是 | 否 | AINLPNodes.ts |
| 111 | network/protocol/udpSend | UDP发送 | 是 | 是 | NetworkProtocolNodes.ts |
| 112 | network/protocol/httpRequest | HTTP请求 | 是 | 是 | NetworkProtocolNodes.ts |
| 113 | string/concat | 字符串连接 | 是 | 是 | StringNodes.ts |
| 114 | string/substring | 子字符串 | 是 | 是 | StringNodes.ts |
| 115 | string/replace | 字符串替换 | 是 | 是 | StringNodes.ts |
| 116 | string/split | 字符串分割 | 是 | 是 | StringNodes.ts |
| 117 | string/length | 字符串长度 | 是 | 是 | StringNodes.ts |
| 118 | string/toUpperCase | 转大写 | 是 | 是 | StringNodes.ts |
| 119 | string/toLowerCase | 转小写 | 是 | 是 | StringNodes.ts |
| 120 | string/trim | 去除空格 | 是 | 是 | StringNodes.ts |
| 121 | array/push | 数组添加 | 是 | 是 | ArrayNodes.ts |
| 122 | array/pop | 数组弹出 | 是 | 是 | ArrayNodes.ts |
| 123 | array/length | 数组长度 | 是 | 是 | ArrayNodes.ts |
| 124 | array/get | 获取元素 | 是 | 是 | ArrayNodes.ts |
| 125 | array/set | 设置元素 | 是 | 是 | ArrayNodes.ts |
| 126 | array/indexOf | 查找索引 | 是 | 否 | ArrayNodes.ts |
| 127 | array/slice | 数组切片 | 是 | 否 | ArrayNodes.ts |
| 128 | array/sort | 数组排序 | 是 | 否 | ArrayNodes.ts |
| 129 | object/getProperty | 获取属性 | 是 | 是 | ObjectNodes.ts |
| 130 | object/setProperty | 设置属性 | 是 | 是 | ObjectNodes.ts |
| 131 | object/hasProperty | 检查属性 | 是 | 是 | ObjectNodes.ts |
| 132 | object/keys | 获取键列表 | 是 | 是 | ObjectNodes.ts |
| 133 | object/values | 获取值列表 | 是 | 是 | ObjectNodes.ts |
| 134 | object/merge | 对象合并 | 是 | 是 | ObjectNodes.ts |
| 135 | object/clone | 对象克隆 | 是 | 是 | ObjectNodes.ts |
| 136 | variable/get | 获取变量 | 是 | 是 | VariableNodes.ts |
| 137 | variable/set | 设置变量 | 是 | 是 | VariableNodes.ts |
| 138 | variable/increment | 变量递增 | 是 | 是 | VariableNodes.ts |
| 139 | variable/decrement | 变量递减 | 是 | 是 | VariableNodes.ts |
| 140 | variable/exists | 变量存在 | 是 | 是 | VariableNodes.ts |
| 141 | variable/delete | 删除变量 | 是 | 是 | VariableNodes.ts |
| 142 | variable/type | 变量类型 | 是 | 是 | VariableNodes.ts |
| 143 | rendering/camera/createPerspectiveCamera | 创建透视相机 | 是 | 是 | RenderingNodes.ts |
| 144 | rendering/camera/createOrthographicCamera | 创建正交相机 | 是 | 是 | RenderingNodes.ts |
| 145 | rendering/camera/setCameraPosition | 设置相机位置 | 是 | 是 | RenderingNodes.ts |
| 146 | rendering/camera/setCameraTarget | 设置相机目标 | 是 | 是 | RenderingNodes.ts |
| 147 | rendering/camera/setCameraFOV | 设置相机视野 | 是 | 是 | RenderingNodes.ts |
| 148 | rendering/light/createDirectionalLight | 创建方向光 | 是 | 是 | RenderingNodes.ts |
| 149 | rendering/light/createPointLight | 创建点光源 | 是 | 是 | RenderingNodes.ts |
| 150 | rendering/light/createSpotLight | 创建聚光灯 | 是 | 是 | RenderingNodes.ts |
| 151 | rendering/light/createAmbientLight | 创建环境光 | 是 | 是 | RenderingNodes.ts |
| 152 | rendering/light/setLightColor | 设置光源颜色 | 是 | 是 | RenderingNodes.ts |
| 153 | rendering/light/setLightIntensity | 设置光源强度 | 是 | 是 | RenderingNodes.ts |
| 154 | rendering/shadow/enableShadows | 启用阴影 | 是 | 是 | RenderingNodes.ts |
| 155 | rendering/shadow/setShadowMapSize | 设置阴影贴图大小 | 是 | 是 | RenderingNodes.ts |
| 156 | rendering/material/createBasicMaterial | 创建基础材质 | 是 | 是 | RenderingNodes.ts |
| 157 | rendering/material/createStandardMaterial | 创建标准材质 | 是 | 是 | RenderingNodes.ts |
| 158 | rendering/material/createPhysicalMaterial | 创建物理材质 | 是 | 是 | RenderingNodes.ts |
| 159 | rendering/material/setMaterialColor | 设置材质颜色 | 是 | 是 | RenderingNodes.ts |
| 160 | rendering/material/setMaterialTexture | 设置材质纹理 | 是 | 是 | RenderingNodes.ts |
| 161 | rendering/material/setMaterialOpacity | 设置材质透明度 | 是 | 是 | RenderingNodes.ts |
| 162 | rendering/postprocess/enableFXAA | 启用抗锯齿 | 是 | 是 | RenderingNodes.ts |
| 163 | rendering/postprocess/enableSSAO | 启用环境光遮蔽 | 是 | 是 | RenderingNodes.ts |
| 164 | rendering/postprocess/enableBloom | 启用辉光效果 | 是 | 是 | RenderingNodes.ts |
| 165 | rendering/lod/setLODLevel | 设置LOD级别 | 是 | 是 | RenderingNodes.ts |
| 166 | physics/rigidbody/createRigidBody | 创建刚体 | 是 | 是 | RenderingNodes.ts |
| 167 | physics/rigidbody/setMass | 设置质量 | 是 | 是 | RenderingNodes.ts |
| 168 | physics/rigidbody/setFriction | 设置摩擦力 | 是 | 是 | RenderingNodes.ts |
| 169 | physics/rigidbody/setRestitution | 设置弹性 | 是 | 是 | RenderingNodes.ts |

### 编辑器集成节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 分类 |
|------|----------|------------|--------------|----------------|------|
| 001 | core/events/onStart | 开始事件 | 是 | 是 | EVENTS |
| 002 | core/events/onUpdate | 更新事件 | 是 | 是 | EVENTS |
| 003 | core/debug/print | 打印 | 是 | 是 | DEBUG |
| 004 | math/basic/add | 加法 | 是 | 是 | MATH |
| 005 | core/flow/delay | 延迟 | 是 | 是 | FLOW |
| 006 | core/events/onEnd | 结束事件 | 是 | 是 | EVENTS |
| 007 | core/events/onPause | 暂停事件 | 是 | 是 | EVENTS |
| 008 | core/events/onResume | 恢复事件 | 是 | 是 | EVENTS |
| 009 | math/basic/subtract | 减法 | 是 | 是 | MATH |
| 010 | math/basic/multiply | 乘法 | 是 | 是 | MATH |
| 011 | math/basic/divide | 除法 | 是 | 是 | MATH |
| 012 | math/trigonometry/sin | 正弦 | 否 | 是 | MATH |
| 013 | math/trigonometry/cos | 余弦 | 否 | 是 | MATH |
| 014 | math/vector/magnitude | 向量长度 | 否 | 是 | MATH |
| 015 | math/vector/normalize | 向量归一化 | 否 | 是 | MATH |
| 016 | math/basic/multiply | 乘法 | 是 | 是 | MATH |
| 017 | math/basic/divide | 除法 | 是 | 是 | MATH |
| 018 | math/basic/modulo | 取模 | 是 | 是 | MATH |
| 019 | logic/comparison/equal | 等于 | 是 | 是 | LOGIC |
| 020 | logic/comparison/notEqual | 不等于 | 是 | 是 | LOGIC |
| 021 | logic/comparison/greater | 大于 | 是 | 是 | LOGIC |
| 022 | logic/comparison/less | 小于 | 是 | 是 | LOGIC |
| 023 | logic/boolean/and | 逻辑与 | 否 | 是 | LOGIC |
| 024 | core/flow/branch | 分支 | 是 | 是 | FLOW |
| 025 | core/flow/sequence | 序列 | 是 | 是 | FLOW |
| 026 | entity/create | 创建实体 | 是 | 是 | ENTITY |
| 027 | entity/destroy | 销毁实体 | 是 | 是 | ENTITY |
| 028 | entity/find | 查找实体 | 是 | 是 | ENTITY |
| 029 | entity/component/add | 添加组件 | 是 | 是 | ENTITY |
| 030 | entity/component/remove | 移除组件 | 是 | 是 | ENTITY |
| 031 | physics/gravity/set | 设置重力 | 否 | 是 | PHYSICS |
| 032 | physics/collision/detect | 碰撞检测 | 否 | 是 | PHYSICS |
| 033 | physics/rigidbody/create | 创建刚体 | 否 | 是 | PHYSICS |
| 034 | physics/force/apply | 施加力 | 否 | 是 | PHYSICS |
| 035 | physics/velocity/set | 设置速度 | 是 | 是 | PHYSICS |
| 036 | entity/get | 获取实体 | 是 | 是 | ENTITY |
| 037 | entity/component/get | 获取组件 | 是 | 是 | ENTITY |
| 038 | entity/transform/getPosition | 获取位置 | 否 | 是 | ENTITY |
| 039 | entity/transform/setPosition | 设置位置 | 否 | 是 | ENTITY |
| 040 | entity/transform/getRotation | 获取旋转 | 否 | 是 | ENTITY |
| 041 | entity/transform/setRotation | 设置旋转 | 否 | 是 | ENTITY |
| 042 | physics/raycast | 射线检测 | 是 | 是 | PHYSICS |
| 043 | physics/applyForce | 应用力 | 是 | 是 | PHYSICS |
| 044 | physics/applyImpulse | 应用冲量 | 否 | 是 | PHYSICS |
| 045 | physics/setVelocity | 设置速度 | 否 | 是 | PHYSICS |
| 046 | physics/getVelocity | 获取速度 | 否 | 是 | PHYSICS |
| 047 | physics/collision/onEnter | 碰撞进入 | 否 | 是 | PHYSICS |
| 048 | physics/collision/onExit | 碰撞退出 | 否 | 是 | PHYSICS |
| 049 | physics/softbody/createCloth | 创建布料 | 是 | 是 | PHYSICS |
| 050 | physics/softbody/createRope | 创建绳索 | 是 | 是 | PHYSICS |
| 051 | physics/softbody/createSoftBody | 创建软体 | 否 | 是 | PHYSICS |
| 052 | physics/softbody/setStiffness | 设置刚度 | 否 | 是 | PHYSICS |
| 053 | physics/softbody/setDamping | 设置阻尼 | 否 | 是 | PHYSICS |
| 054 | network/connectToServer | 连接到服务器 | 是 | 是 | NETWORK |
| 055 | network/sendMessage | 发送网络消息 | 是 | 是 | NETWORK |
| 056 | network/events/onMessage | 接收网络消息 | 是 | 是 | NETWORK |
| 057 | network/disconnect | 断开连接 | 否 | 是 | NETWORK |
| 058 | ai/animation/generateBodyAnimation | 生成身体动画 | 是 | 是 | AI |
| 059 | ai/animation/generateFacialAnimation | 生成面部动画 | 是 | 是 | AI |
| 060 | ai/model/load | 加载AI模型 | 否 | 是 | AI |
| 061 | ai/model/generateText | 生成文本 | 否 | 是 | AI |
| 062 | core/events/onEnd | 结束事件 | 是 | 是 | EVENTS |
| 063 | core/events/onPause | 暂停事件 | 是 | 是 | EVENTS |
| 064 | time/delay | 延迟 | 否 | 是 | TIME |
| 065 | time/timer | 计时器 | 否 | 是 | TIME |
| 066 | time/delay | 延迟 | 否 | 是 | TIME |
| 067 | time/timer | 计时器 | 否 | 是 | TIME |
| 068 | animation/playAnimation | 播放动画 | 否 | 是 | ANIMATION |
| 069 | animation/stopAnimation | 停止动画 | 否 | 是 | ANIMATION |
| 070 | animation/setAnimationSpeed | 设置动画速度 | 否 | 是 | ANIMATION |
| 071 | animation/getAnimationState | 获取动画状态 | 否 | 是 | ANIMATION |
| 072 | input/keyboard | 键盘输入 | 否 | 是 | INPUT |
| 073 | input/mouse | 鼠标输入 | 否 | 是 | INPUT |
| 074 | input/gamepad | 游戏手柄输入 | 否 | 是 | INPUT |
| 075 | audio/playAudio | 播放音频 | 否 | 是 | AUDIO |
| 076 | audio/stopAudio | 停止音频 | 否 | 是 | AUDIO |
| 077 | audio/setVolume | 设置音量 | 否 | 是 | AUDIO |
| 078 | audio/analyzer | 音频分析 | 否 | 是 | AUDIO |
| 079 | audio/audio3D | 3D音频 | 否 | 是 | AUDIO |
| 080 | debug/breakpoint | 断点 | 是 | 是 | DEBUG |
| 081 | debug/log | 日志 | 是 | 是 | DEBUG |
| 082 | debug/performanceTimer | 性能计时 | 是 | 是 | DEBUG |
| 083 | debug/variableWatch | 变量监视 | 是 | 是 | DEBUG |
| 084 | debug/assert | 断言 | 是 | 是 | DEBUG |
| 085 | network/security/encryptData | 数据加密 | 是 | 是 | NETWORK |
| 086 | network/security/decryptData | 数据解密 | 是 | 是 | NETWORK |
| 087 | network/security/hashData | 数据哈希 | 否 | 是 | NETWORK |
| 088 | network/security/authenticateUser | 用户认证 | 是 | 是 | NETWORK |
| 089 | network/security/validateSession | 验证会话 | 是 | 是 | NETWORK |
| 090 | network/webrtc/createConnection | 创建WebRTC连接 | 是 | 是 | NETWORK |
| 091 | network/webrtc/sendDataChannelMessage | 发送数据通道消息 | 是 | 是 | NETWORK |
| 092 | network/webrtc/createDataChannel | 创建数据通道 | 否 | 是 | NETWORK |
| 093 | network/webrtc/closeConnection | 关闭WebRTC连接 | 否 | 是 | NETWORK |
| 094 | ai/emotion/analyze | 情感分析 | 是 | 是 | AI |
| 095 | ai/emotion/driveAnimation | 情感驱动动画 | 是 | 是 | AI |
| 096 | ai/nlp/classifyText | 文本分类 | 是 | 是 | AI |
| 097 | ai/nlp/recognizeEntities | 命名实体识别 | 是 | 是 | AI |
| 098 | ai/nlp/analyzeSentiment | 情感分析 | 否 | 是 | AI |
| 099 | ai/nlp/extractKeywords | 关键词提取 | 否 | 是 | AI |
| 100 | network/protocol/udpSend | UDP发送 | 是 | 是 | NETWORK |
| 101 | network/protocol/httpRequest | HTTP请求 | 是 | 是 | NETWORK |
| 102 | network/protocol/tcpConnect | TCP连接 | 否 | 是 | NETWORK |
| 103 | string/concat | 字符串连接 | 是 | 是 | STRING |
| 104 | string/substring | 子字符串 | 是 | 是 | STRING |
| 105 | string/replace | 字符串替换 | 是 | 是 | STRING |
| 106 | string/split | 字符串分割 | 是 | 是 | STRING |
| 107 | string/length | 字符串长度 | 是 | 是 | STRING |
| 108 | string/toUpperCase | 转大写 | 是 | 是 | STRING |
| 109 | string/toLowerCase | 转小写 | 是 | 是 | STRING |
| 110 | string/trim | 去除空格 | 是 | 是 | STRING |
| 111 | array/push | 数组添加 | 是 | 是 | ARRAY |
| 112 | array/pop | 数组弹出 | 是 | 是 | ARRAY |
| 113 | array/length | 数组长度 | 是 | 是 | ARRAY |
| 114 | array/get | 获取元素 | 是 | 是 | ARRAY |
| 115 | array/set | 设置元素 | 是 | 是 | ARRAY |
| 116 | object/getProperty | 获取属性 | 是 | 是 | OBJECT |
| 117 | object/setProperty | 设置属性 | 是 | 是 | OBJECT |
| 118 | object/hasProperty | 检查属性 | 是 | 是 | OBJECT |
| 119 | object/keys | 获取键列表 | 是 | 是 | OBJECT |
| 120 | object/values | 获取值列表 | 是 | 是 | OBJECT |
| 121 | object/merge | 对象合并 | 是 | 是 | OBJECT |
| 122 | object/clone | 对象克隆 | 是 | 是 | OBJECT |
| 123 | variable/get | 获取变量 | 是 | 是 | VARIABLE |
| 124 | variable/set | 设置变量 | 是 | 是 | VARIABLE |
| 125 | variable/increment | 变量递增 | 是 | 是 | VARIABLE |
| 126 | variable/decrement | 变量递减 | 是 | 是 | VARIABLE |
| 127 | variable/exists | 变量存在 | 是 | 是 | VARIABLE |
| 128 | variable/delete | 删除变量 | 是 | 是 | VARIABLE |
| 129 | variable/type | 变量类型 | 是 | 是 | VARIABLE |
| 130 | rendering/camera/createPerspectiveCamera | 创建透视相机 | 是 | 是 | RENDERING |
| 131 | rendering/camera/createOrthographicCamera | 创建正交相机 | 是 | 是 | RENDERING |
| 132 | rendering/camera/setCameraPosition | 设置相机位置 | 是 | 是 | RENDERING |
| 133 | rendering/camera/setCameraTarget | 设置相机目标 | 是 | 是 | RENDERING |
| 134 | rendering/camera/setCameraFOV | 设置相机视野 | 是 | 是 | RENDERING |
| 135 | rendering/light/createDirectionalLight | 创建方向光 | 是 | 是 | RENDERING |
| 136 | rendering/light/createPointLight | 创建点光源 | 是 | 是 | RENDERING |
| 137 | rendering/light/createSpotLight | 创建聚光灯 | 是 | 是 | RENDERING |
| 138 | rendering/light/createAmbientLight | 创建环境光 | 是 | 是 | RENDERING |
| 139 | rendering/light/setLightColor | 设置光源颜色 | 是 | 是 | RENDERING |
| 140 | rendering/light/setLightIntensity | 设置光源强度 | 是 | 是 | RENDERING |
| 141 | rendering/shadow/enableShadows | 启用阴影 | 是 | 是 | RENDERING |
| 142 | rendering/shadow/setShadowMapSize | 设置阴影贴图大小 | 是 | 是 | RENDERING |
| 143 | rendering/material/createBasicMaterial | 创建基础材质 | 是 | 是 | RENDERING |
| 144 | rendering/material/createStandardMaterial | 创建标准材质 | 是 | 是 | RENDERING |
| 145 | rendering/material/createPhysicalMaterial | 创建物理材质 | 是 | 是 | RENDERING |
| 146 | rendering/material/setMaterialColor | 设置材质颜色 | 是 | 是 | RENDERING |
| 147 | rendering/material/setMaterialTexture | 设置材质纹理 | 是 | 是 | RENDERING |
| 148 | rendering/material/setMaterialOpacity | 设置材质透明度 | 是 | 是 | RENDERING |
| 149 | rendering/postprocess/enableFXAA | 启用抗锯齿 | 是 | 是 | RENDERING |
| 150 | rendering/postprocess/enableSSAO | 启用环境光遮蔽 | 是 | 是 | RENDERING |
| 151 | rendering/postprocess/enableBloom | 启用辉光效果 | 是 | 是 | RENDERING |
| 152 | rendering/lod/setLODLevel | 设置LOD级别 | 是 | 是 | RENDERING |
| 153 | physics/rigidbody/createRigidBody | 创建刚体 | 是 | 是 | PHYSICS |
| 154 | physics/rigidbody/setMass | 设置质量 | 是 | 是 | PHYSICS |
| 155 | physics/rigidbody/setFriction | 设置摩擦力 | 是 | 是 | PHYSICS |
| 156 | physics/rigidbody/setRestitution | 设置弹性 | 是 | 是 | PHYSICS |
| 157 | physics/advanced/createSoftBody | 创建软体 | 否 | 是 | PHYSICS |
| 158 | physics/advanced/createFluid | 创建流体 | 否 | 是 | PHYSICS |
| 159 | physics/advanced/createCloth | 创建布料 | 否 | 是 | PHYSICS |
| 160 | physics/advanced/createParticleSystem | 创建粒子系统 | 否 | 是 | PHYSICS |
| 161 | physics/advanced/setGravity | 设置重力 | 否 | 是 | PHYSICS |
| 162 | physics/advanced/createJoint | 创建关节 | 否 | 是 | PHYSICS |
| 163 | physics/advanced/setDamping | 设置阻尼 | 否 | 是 | PHYSICS |
| 164 | physics/advanced/createConstraint | 创建约束 | 否 | 是 | PHYSICS |
| 165 | physics/advanced/simulateWind | 模拟风力 | 否 | 是 | PHYSICS |
| 166 | physics/advanced/createExplosion | 创建爆炸 | 否 | 是 | PHYSICS |
| 167 | physics/collision/onCollisionExit | 碰撞结束事件 | 是 | 是 | EVENTS |
| 168 | physics/collision/onTriggerEnter | 触发器进入事件 | 是 | 是 | EVENTS |
| 169 | physics/collision/onTriggerExit | 触发器退出事件 | 是 | 是 | EVENTS |
| 170 | physics/world/setGravity | 设置重力 | 是 | 是 | PHYSICS |
| 171 | physics/world/setTimeStep | 设置时间步长 | 是 | 是 | PHYSICS |
| 172 | physics/character/createCharacterController | 创建角色控制器 | 是 | 是 | PHYSICS |
| 173 | physics/character/moveCharacter | 移动角色 | 是 | 是 | PHYSICS |
| 174 | physics/character/jumpCharacter | 角色跳跃 | 是 | 是 | PHYSICS |
| 175 | physics/vehicle/createVehicle | 创建载具 | 是 | 是 | PHYSICS |
| 176 | physics/vehicle/setEngineForce | 设置引擎力 | 是 | 是 | PHYSICS |
| 177 | physics/vehicle/setBrakeForce | 设置制动力 | 是 | 是 | PHYSICS |
| 178 | physics/vehicle/setSteeringValue | 设置转向值 | 是 | 是 | PHYSICS |
| 179 | physics/fluid/createFluidSimulation | 创建流体模拟 | 是 | 是 | PHYSICS |
| 180 | physics/cloth/createClothSimulation | 创建布料模拟 | 是 | 是 | PHYSICS |
| 181 | physics/destruction/createDestructible | 创建可破坏物体 | 是 | 是 | PHYSICS |
| 182 | animation/clip/createAnimationClip | 创建动画片段 | 是 | 是 | ANIMATION |
| 183 | animation/clip/addKeyframe | 添加关键帧 | 是 | 是 | ANIMATION |
| 184 | animation/clip/setInterpolation | 设置插值方式 | 是 | 是 | ANIMATION |
| 185 | animation/mixer/createAnimationMixer | 创建动画混合器 | 是 | 是 | ANIMATION |
| 186 | animation/mixer/playClip | 播放动画片段 | 否 | 是 | ANIMATION |
| 187 | animation/mixer/stopClip | 停止动画片段 | 否 | 是 | ANIMATION |
| 188 | animation/mixer/crossFade | 交叉淡化 | 否 | 是 | ANIMATION |
| 189 | animation/mixer/setWeight | 设置动画权重 | 否 | 是 | ANIMATION |
| 190 | animation/bone/getBoneTransform | 获取骨骼变换 | 否 | 是 | ANIMATION |
| 191 | animation/bone/setBoneTransform | 设置骨骼变换 | 否 | 是 | ANIMATION |
| 192 | animation/ik/createIKChain | 创建IK链 | 否 | 是 | ANIMATION |
| 193 | animation/ik/solveIK | 解算IK | 是 | 是 | ANIMATION |
| 194 | animation/morph/createMorphTarget | 创建变形目标 | 是 | 是 | ANIMATION |
| 195 | animation/morph/setMorphWeight | 设置变形权重 | 是 | 是 | ANIMATION |
| 196 | animation/curve/createAnimationCurve | 创建动画曲线 | 是 | 是 | ANIMATION |
| 197 | animation/curve/evaluateCurve | 计算曲线值 | 是 | 是 | ANIMATION |
| 198 | animation/state/createStateMachine | 创建状态机 | 否 | 是 | ANIMATION |
| 199 | animation/state/addState | 添加状态 | 否 | 是 | ANIMATION |
| 200 | animation/state/addTransition | 添加过渡 | 否 | 是 | ANIMATION |
| 201 | animation/state/setCurrentState | 设置当前状态 | 否 | 是 | ANIMATION |
| 202 | audio/source/create3DAudioSource | 创建3D音频源 | 否 | 是 | AUDIO |
| 203 | audio/source/setAudioPosition | 设置音频位置 | 否 | 是 | AUDIO |
| 204 | audio/source/setAudioVelocity | 设置音频速度 | 否 | 是 | AUDIO |
| 205 | audio/listener/setListenerPosition | 设置听者位置 | 否 | 是 | AUDIO |
| 206 | audio/listener/setListenerOrientation | 设置听者朝向 | 否 | 是 | AUDIO |
| 207 | audio/effect/createReverb | 创建混响效果 | 否 | 是 | AUDIO |
| 208 | audio/effect/createEcho | 创建回声效果 | 否 | 是 | AUDIO |
| 209 | audio/effect/createFilter | 创建滤波器 | 否 | 是 | AUDIO |
| 210 | audio/analysis/createAnalyzer | 创建音频分析器 | 否 | 是 | AUDIO |
| 211 | audio/analysis/getFrequencyData | 获取频率数据 | 否 | 是 | AUDIO |
| 212 | audio/analysis/getWaveformData | 获取波形数据 | 否 | 是 | AUDIO |
| 213 | audio/streaming/createAudioStream | 创建音频流 | 否 | 是 | AUDIO |
| 214 | audio/streaming/connectStream | 连接音频流 | 否 | 是 | AUDIO |
| 215 | audio/recording/startRecording | 开始录音 | 否 | 是 | AUDIO |
| 216 | audio/recording/stopRecording | 停止录音 | 否 | 是 | AUDIO |
| 217 | scene/management/createScene | 创建场景 | 否 | 是 | ENTITY |
| 218 | scene/management/loadScene | 加载场景 | 否 | 是 | ENTITY |
| 219 | scene/management/saveScene | 保存场景 | 否 | 是 | ENTITY |
| 220 | scene/management/switchScene | 切换场景 | 否 | 是 | ENTITY |
| 221 | scene/management/addToScene | 添加到场景 | 否 | 是 | ENTITY |
| 222 | scene/management/removeFromScene | 从场景移除 | 否 | 是 | ENTITY |
| 223 | scene/culling/enableFrustumCulling | 启用视锥体剔除 | 否 | 是 | ENTITY |
| 224 | scene/culling/enableOcclusionCulling | 启用遮挡剔除 | 否 | 是 | ENTITY |
| 225 | scene/optimization/enableBatching | 启用批处理 | 否 | 是 | ENTITY |
| 226 | scene/optimization/enableInstancing | 启用实例化 | 否 | 是 | ENTITY |
| 227 | scene/skybox/setSkybox | 设置天空盒 | 否 | 是 | ENTITY |
| 228 | scene/fog/enableFog | 启用雾效 | 否 | 是 | ENTITY |
| 229 | scene/fog/setFogColor | 设置雾颜色 | 否 | 是 | ENTITY |
| 230 | scene/fog/setFogDensity | 设置雾密度 | 否 | 是 | ENTITY |
| 231 | scene/environment/setEnvironmentMap | 设置环境贴图 | 否 | 是 | ENTITY |
| 232 | particles/system/createParticleSystem | 创建粒子系统 | 否 | 是 | ENTITY |
| 233 | particles/emitter/createEmitter | 创建发射器 | 否 | 是 | ENTITY |
| 234 | particles/emitter/setEmissionRate | 设置发射速率 | 否 | 是 | ENTITY |
| 235 | particles/emitter/setEmissionShape | 设置发射形状 | 否 | 是 | ENTITY |
| 236 | particles/particle/setLifetime | 设置粒子寿命 | 否 | 是 | ENTITY |
| 237 | particles/particle/setVelocity | 设置粒子速度 | 否 | 是 | ENTITY |
| 238 | particles/particle/setSize | 设置粒子大小 | 否 | 是 | ENTITY |
| 239 | particles/particle/setColor | 设置粒子颜色 | 否 | 是 | ENTITY |
| 240 | particles/forces/addGravity | 添加重力 | 否 | 是 | ENTITY |
| 241 | particles/forces/addWind | 添加风力 | 否 | 是 | ENTITY |
| 242 | particles/forces/addTurbulence | 添加湍流 | 否 | 是 | ENTITY |
| 243 | particles/collision/enableCollision | 启用粒子碰撞 | 否 | 是 | ENTITY |
| 244 | particles/material/setParticleMaterial | 设置粒子材质 | 否 | 是 | ENTITY |
| 245 | particles/animation/animateSize | 动画粒子大小 | 否 | 是 | ANIMATION |
| 246 | particles/animation/animateColor | 动画粒子颜色 | 否 | 是 | ANIMATION |
| 247 | terrain/generation/createTerrain | 创建地形 | 否 | 是 | ENTITY |
| 248 | terrain/generation/generateHeightmap | 生成高度图 | 否 | 是 | ENTITY |
| 249 | terrain/generation/applyNoise | 应用噪声 | 否 | 是 | ENTITY |
| 250 | terrain/texture/setTerrainTexture | 设置地形纹理 | 否 | 是 | ENTITY |
| 251 | terrain/texture/blendTextures | 混合纹理 | 否 | 是 | ENTITY |
| 252 | terrain/lod/enableTerrainLOD | 启用地形LOD | 否 | 是 | ENTITY |
| 253 | terrain/collision/enableTerrainCollision | 启用地形碰撞 | 否 | 是 | ENTITY |
| 254 | water/system/createWaterSurface | 创建水面 | 否 | 是 | ENTITY |
| 255 | water/waves/addWaves | 添加波浪 | 否 | 是 | ENTITY |
| 256 | water/reflection/enableReflection | 启用水面反射 | 否 | 是 | ENTITY |
| 257 | water/refraction/enableRefraction | 启用水面折射 | 否 | 是 | ENTITY |
| 258 | vegetation/system/createVegetation | 创建植被 | 否 | 是 | ENTITY |
| 259 | vegetation/grass/addGrass | 添加草地 | 否 | 是 | ENTITY |
| 260 | vegetation/trees/addTrees | 添加树木 | 否 | 是 | ENTITY |
| 261 | weather/system/createWeatherSystem | 创建天气系统 | 否 | 是 | ENTITY |
| 262 | weather/rain/enableRain | 启用雨效 | 否 | 是 | ENTITY |
| 263 | weather/snow/enableSnow | 启用雪效 | 否 | 是 | ENTITY |
| 264 | weather/wind/setWindDirection | 设置风向 | 否 | 是 | ENTITY |
| 265 | weather/wind/setWindStrength | 设置风力 | 否 | 是 | ENTITY |
| 266 | environment/time/setTimeOfDay | 设置时间 | 否 | 是 | ENTITY |
| 267 | editor/project/createProject | 创建项目 | 否 | 是 | CUSTOM |
| 268 | editor/project/openProject | 打开项目 | 否 | 是 | CUSTOM |
| 269 | editor/project/saveProject | 保存项目 | 否 | 是 | CUSTOM |
| 270 | editor/project/closeProject | 关闭项目 | 否 | 是 | CUSTOM |
| 271 | editor/project/exportProject | 导出项目 | 否 | 是 | CUSTOM |
| 272 | editor/project/importProject | 导入项目 | 否 | 是 | CUSTOM |
| 273 | editor/project/setProjectSettings | 设置项目配置 | 否 | 是 | CUSTOM |
| 274 | editor/project/getProjectInfo | 获取项目信息 | 否 | 是 | CUSTOM |
| 275 | editor/asset/importAsset | 导入资产 | 否 | 是 | CUSTOM |
| 276 | editor/asset/deleteAsset | 删除资产 | 否 | 是 | CUSTOM |
| 277 | editor/asset/renameAsset | 重命名资产 | 否 | 是 | CUSTOM |
| 278 | editor/asset/moveAsset | 移动资产 | 否 | 是 | CUSTOM |
| 279 | editor/asset/createFolder | 创建文件夹 | 否 | 是 | CUSTOM |
| 280 | editor/asset/getAssetInfo | 获取资产信息 | 否 | 是 | CUSTOM |
| 281 | editor/asset/generateThumbnail | 生成缩略图 | 否 | 是 | CUSTOM |
| 282 | editor/scene/createEntity | 创建实体 | 否 | 是 | ENTITY |
| 283 | editor/scene/deleteEntity | 删除实体 | 否 | 是 | ENTITY |
| 284 | editor/scene/selectEntity | 选择实体 | 否 | 是 | ENTITY |
| 285 | editor/scene/duplicateEntity | 复制实体 | 否 | 是 | ENTITY |
| 286 | editor/scene/groupEntities | 组合实体 | 否 | 是 | ENTITY |
| 287 | editor/scene/ungroupEntities | 取消组合 | 否 | 是 | ENTITY |
| 288 | editor/scene/setEntityParent | 设置父对象 | 否 | 是 | ENTITY |
| 289 | editor/scene/moveEntity | 移动实体 | 否 | 是 | ENTITY |
| 290 | editor/scene/rotateEntity | 旋转实体 | 否 | 是 | ENTITY |
| 291 | editor/scene/scaleEntity | 缩放实体 | 否 | 是 | ENTITY |
| 292 | editor/scene/hideEntity | 隐藏实体 | 否 | 是 | ENTITY |
| 293 | editor/scene/showEntity | 显示实体 | 否 | 是 | ENTITY |
| 294 | editor/scene/lockEntity | 锁定实体 | 否 | 是 | ENTITY |
| 295 | editor/scene/unlockEntity | 解锁实体 | 否 | 是 | ENTITY |
| 296 | editor/scene/focusOnEntity | 聚焦实体 | 否 | 是 | ENTITY |
| 297 | editor/ui/createUIElement | 创建UI元素 | 否 | 是 | CUSTOM |
| 298 | editor/ui/deleteUIElement | 删除UI元素 | 否 | 是 | CUSTOM |
| 299 | editor/ui/setUIPosition | 设置UI位置 | 否 | 是 | CUSTOM |
| 300 | editor/ui/setUISize | 设置UI大小 | 否 | 是 | CUSTOM |
| 301 | editor/ui/setUIText | 设置UI文本 | 否 | 是 | CUSTOM |
| 302 | editor/ui/setUIColor | 设置UI颜色 | 否 | 是 | CUSTOM |
| 303 | editor/ui/setUIFont | 设置UI字体 | 否 | 是 | CUSTOM |
| 304 | editor/ui/setUIImage | 设置UI图像 | 否 | 是 | CUSTOM |
| 305 | editor/ui/addUIEvent | 添加UI事件 | 否 | 是 | CUSTOM |
| 306 | editor/ui/removeUIEvent | 移除UI事件 | 否 | 是 | CUSTOM |
| 307 | editor/ui/setUIVisible | 设置UI可见性 | 否 | 是 | CUSTOM |
| 308 | editor/ui/setUIEnabled | 设置UI启用状态 | 否 | 是 | CUSTOM |
| 309 | editor/ui/setUILayer | 设置UI层级 | 否 | 是 | CUSTOM |
| 310 | editor/ui/alignUIElements | 对齐UI元素 | 否 | 是 | CUSTOM |
| 311 | editor/ui/distributeUIElements | 分布UI元素 | 否 | 是 | CUSTOM |
| 312 | editor/tools/enableGizmo | 启用操作手柄 | 否 | 是 | CUSTOM |
| 313 | editor/tools/setGizmoMode | 设置手柄模式 | 否 | 是 | CUSTOM |
| 314 | editor/tools/enableGrid | 启用网格 | 否 | 是 | CUSTOM |
| 315 | editor/tools/setGridSize | 设置网格大小 | 否 | 是 | CUSTOM |
| 316 | editor/tools/enableSnap | 启用吸附 | 否 | 是 | CUSTOM |
| 317 | server/user/registerUser | 用户注册 | 否 | 是 | NETWORK |
| 318 | server/user/loginUser | 用户登录 | 否 | 是 | NETWORK |
| 319 | server/user/logoutUser | 用户登出 | 否 | 是 | NETWORK |
| 320 | server/user/updateUserProfile | 更新用户资料 | 否 | 是 | NETWORK |
| 321 | server/user/changePassword | 修改密码 | 否 | 是 | NETWORK |
| 322 | server/user/resetPassword | 重置密码 | 否 | 是 | NETWORK |
| 323 | server/user/getUserInfo | 获取用户信息 | 否 | 是 | NETWORK |
| 324 | server/user/deleteUser | 删除用户 | 否 | 是 | NETWORK |
| 325 | server/user/setUserRole | 设置用户角色 | 否 | 是 | NETWORK |
| 326 | server/user/validateToken | 验证令牌 | 否 | 是 | NETWORK |
| 327 | server/project/createProject | 创建服务器项目 | 否 | 是 | NETWORK |
| 328 | server/project/deleteProject | 删除服务器项目 | 否 | 是 | NETWORK |
| 329 | server/project/updateProject | 更新项目信息 | 否 | 是 | NETWORK |
| 330 | server/project/getProjectList | 获取项目列表 | 否 | 是 | NETWORK |
| 331 | server/project/getProjectDetails | 获取项目详情 | 否 | 是 | NETWORK |
| 332 | server/project/shareProject | 分享项目 | 否 | 是 | NETWORK |
| 333 | server/project/unshareProject | 取消分享 | 否 | 是 | NETWORK |
| 334 | server/project/setProjectPermission | 设置项目权限 | 否 | 是 | NETWORK |
| 335 | server/project/forkProject | 复制项目 | 否 | 是 | NETWORK |
| 336 | server/project/archiveProject | 归档项目 | 否 | 是 | NETWORK |
| 337 | server/project/restoreProject | 恢复项目 | 否 | 是 | NETWORK |
| 338 | server/project/exportProjectData | 导出项目数据 | 否 | 是 | NETWORK |
| 339 | server/project/importProjectData | 导入项目数据 | 否 | 是 | NETWORK |
| 340 | server/project/getProjectStats | 获取项目统计 | 否 | 是 | NETWORK |
| 341 | server/project/backupProject | 备份项目 | 否 | 是 | NETWORK |
| 342 | server/asset/uploadAsset | 上传资产 | 否 | 是 | NETWORK |
| 343 | server/asset/downloadAsset | 下载资产 | 否 | 是 | NETWORK |
| 344 | server/asset/deleteAsset | 删除服务器资产 | 否 | 是 | NETWORK |
| 345 | server/asset/getAssetList | 获取资产列表 | 否 | 是 | NETWORK |
| 346 | server/asset/getAssetInfo | 获取资产信息 | 否 | 是 | NETWORK |
| 347 | server/asset/updateAssetInfo | 更新资产信息 | 否 | 是 | NETWORK |
| 348 | server/asset/moveAssetToFolder | 移动资产到文件夹 | 否 | 是 | NETWORK |
| 349 | server/asset/createAssetFolder | 创建资产文件夹 | 否 | 是 | NETWORK |
| 350 | server/asset/deleteAssetFolder | 删除资产文件夹 | 否 | 是 | NETWORK |
| 351 | server/asset/shareAsset | 分享资产 | 否 | 是 | NETWORK |
| 352 | server/asset/getAssetVersions | 获取资产版本 | 否 | 是 | NETWORK |
| 353 | server/asset/createAssetVersion | 创建资产版本 | 否 | 是 | NETWORK |
| 354 | server/asset/restoreAssetVersion | 恢复资产版本 | 否 | 是 | NETWORK |
| 355 | server/asset/generateAssetThumbnail | 生成资产缩略图 | 否 | 是 | NETWORK |
| 356 | server/asset/optimizeAsset | 优化资产 | 否 | 是 | NETWORK |
| 357 | server/collaboration/joinRoom | 加入协作房间 | 否 | 是 | NETWORK |
| 358 | server/collaboration/leaveRoom | 离开协作房间 | 否 | 是 | NETWORK |
| 359 | server/collaboration/sendOperation | 发送协作操作 | 否 | 是 | NETWORK |
| 360 | server/collaboration/receiveOperation | 接收协作操作 | 否 | 是 | NETWORK |
| 361 | server/collaboration/resolveConflict | 解决编辑冲突 | 否 | 是 | NETWORK |
| 362 | server/collaboration/getOnlineUsers | 获取在线用户 | 否 | 是 | NETWORK |
| 363 | server/collaboration/broadcastMessage | 广播消息 | 否 | 是 | NETWORK |
| 364 | server/collaboration/lockResource | 锁定资源 | 否 | 是 | NETWORK |
| 365 | server/collaboration/unlockResource | 解锁资源 | 否 | 是 | NETWORK |
| 366 | server/collaboration/syncState | 同步状态 | 否 | 是 | NETWORK |

## 🔍 问题分析

### 仅在引擎注册但未集成到编辑器的节点
001. logic/flow/branch - 分支
002. logic/comparison/greaterEqual - 大于等于
003. logic/comparison/lessEqual - 小于等于
004. logic/operation/and - 与
005. logic/operation/or - 或
006. logic/operation/not - 非
007. logic/flow/toggle - 开关
008. math/advanced/power - 幂运算
009. math/advanced/sqrt - 平方根
010. math/trigonometric/sin - 正弦
011. math/trigonometric/cos - 余弦
012. math/trigonometric/tan - 正切
013. entity/component/has - 检查组件
014. physics/collisionDetection - 碰撞检测
015. physics/createConstraint - 创建约束
016. physics/createMaterial - 创建物理材质
017. physics/softbody/createBalloon - 创建气球
018. physics/softbody/createJelly - 创建果冻
019. physics/softbody/cut - 切割软体
020. animation/legacy/playAnimation - 播放动画
021. animation/legacy/stopAnimation - 停止动画
022. animation/legacy/setAnimationSpeed - 设置动画速度
023. animation/legacy/getAnimationState - 获取动画状态
024. animation/mixer/playAnimationAction - 播放动画动作
025. animation/skeleton/createSkeletalAnimation - 创建骨骼动画
026. animation/skeleton/setBoneTransform - 设置骨骼变换
027. animation/ik/createIKConstraint - 创建IK约束
028. animation/statemachine/createStateMachine - 创建状态机
029. animation/statemachine/transitionState - 状态转换
030. network/security/computeHash - 计算哈希
031. network/security/generateSignature - 生成签名
032. network/security/verifySignature - 验证签名
033. network/security/createSession - 创建会话
034. network/webrtc/onDataChannelMessage - 数据通道消息事件
035. ai/nlp/generateSummary - 生成文本摘要
036. ai/nlp/translateText - 语言翻译
037. array/indexOf - 查找索引
038. array/slice - 数组切片
039. array/sort - 数组排序

### 仅在编辑器集成但未注册到引擎的节点
001. math/trigonometry/sin - 正弦
002. math/trigonometry/cos - 余弦
003. math/vector/magnitude - 向量长度
004. math/vector/normalize - 向量归一化
005. logic/boolean/and - 逻辑与
006. physics/gravity/set - 设置重力
007. physics/collision/detect - 碰撞检测
008. physics/rigidbody/create - 创建刚体
009. physics/force/apply - 施加力
010. entity/transform/getPosition - 获取位置
011. entity/transform/setPosition - 设置位置
012. entity/transform/getRotation - 获取旋转
013. entity/transform/setRotation - 设置旋转
014. physics/applyImpulse - 应用冲量
015. physics/setVelocity - 设置速度
016. physics/getVelocity - 获取速度
017. physics/collision/onEnter - 碰撞进入
018. physics/collision/onExit - 碰撞退出
019. physics/softbody/createSoftBody - 创建软体
020. physics/softbody/setStiffness - 设置刚度
021. physics/softbody/setDamping - 设置阻尼
022. network/disconnect - 断开连接
023. ai/model/load - 加载AI模型
024. ai/model/generateText - 生成文本
025. time/delay - 延迟
026. time/timer - 计时器
027. time/delay - 延迟
028. time/timer - 计时器
029. animation/playAnimation - 播放动画
030. animation/stopAnimation - 停止动画
031. animation/setAnimationSpeed - 设置动画速度
032. animation/getAnimationState - 获取动画状态
033. input/keyboard - 键盘输入
034. input/mouse - 鼠标输入
035. input/gamepad - 游戏手柄输入
036. audio/playAudio - 播放音频
037. audio/stopAudio - 停止音频
038. audio/setVolume - 设置音量
039. audio/analyzer - 音频分析
040. audio/audio3D - 3D音频
041. network/security/hashData - 数据哈希
042. network/webrtc/createDataChannel - 创建数据通道
043. network/webrtc/closeConnection - 关闭WebRTC连接
044. ai/nlp/analyzeSentiment - 情感分析
045. ai/nlp/extractKeywords - 关键词提取
046. network/protocol/tcpConnect - TCP连接
047. physics/advanced/createSoftBody - 创建软体
048. physics/advanced/createFluid - 创建流体
049. physics/advanced/createCloth - 创建布料
050. physics/advanced/createParticleSystem - 创建粒子系统
051. physics/advanced/setGravity - 设置重力
052. physics/advanced/createJoint - 创建关节
053. physics/advanced/setDamping - 设置阻尼
054. physics/advanced/createConstraint - 创建约束
055. physics/advanced/simulateWind - 模拟风力
056. physics/advanced/createExplosion - 创建爆炸
057. animation/mixer/playClip - 播放动画片段
058. animation/mixer/stopClip - 停止动画片段
059. animation/mixer/crossFade - 交叉淡化
060. animation/mixer/setWeight - 设置动画权重
061. animation/bone/getBoneTransform - 获取骨骼变换
062. animation/bone/setBoneTransform - 设置骨骼变换
063. animation/ik/createIKChain - 创建IK链
064. animation/state/createStateMachine - 创建状态机
065. animation/state/addState - 添加状态
066. animation/state/addTransition - 添加过渡
067. animation/state/setCurrentState - 设置当前状态
068. audio/source/create3DAudioSource - 创建3D音频源
069. audio/source/setAudioPosition - 设置音频位置
070. audio/source/setAudioVelocity - 设置音频速度
071. audio/listener/setListenerPosition - 设置听者位置
072. audio/listener/setListenerOrientation - 设置听者朝向
073. audio/effect/createReverb - 创建混响效果
074. audio/effect/createEcho - 创建回声效果
075. audio/effect/createFilter - 创建滤波器
076. audio/analysis/createAnalyzer - 创建音频分析器
077. audio/analysis/getFrequencyData - 获取频率数据
078. audio/analysis/getWaveformData - 获取波形数据
079. audio/streaming/createAudioStream - 创建音频流
080. audio/streaming/connectStream - 连接音频流
081. audio/recording/startRecording - 开始录音
082. audio/recording/stopRecording - 停止录音
083. scene/management/createScene - 创建场景
084. scene/management/loadScene - 加载场景
085. scene/management/saveScene - 保存场景
086. scene/management/switchScene - 切换场景
087. scene/management/addToScene - 添加到场景
088. scene/management/removeFromScene - 从场景移除
089. scene/culling/enableFrustumCulling - 启用视锥体剔除
090. scene/culling/enableOcclusionCulling - 启用遮挡剔除
091. scene/optimization/enableBatching - 启用批处理
092. scene/optimization/enableInstancing - 启用实例化
093. scene/skybox/setSkybox - 设置天空盒
094. scene/fog/enableFog - 启用雾效
095. scene/fog/setFogColor - 设置雾颜色
096. scene/fog/setFogDensity - 设置雾密度
097. scene/environment/setEnvironmentMap - 设置环境贴图
098. particles/system/createParticleSystem - 创建粒子系统
099. particles/emitter/createEmitter - 创建发射器
100. particles/emitter/setEmissionRate - 设置发射速率
101. particles/emitter/setEmissionShape - 设置发射形状
102. particles/particle/setLifetime - 设置粒子寿命
103. particles/particle/setVelocity - 设置粒子速度
104. particles/particle/setSize - 设置粒子大小
105. particles/particle/setColor - 设置粒子颜色
106. particles/forces/addGravity - 添加重力
107. particles/forces/addWind - 添加风力
108. particles/forces/addTurbulence - 添加湍流
109. particles/collision/enableCollision - 启用粒子碰撞
110. particles/material/setParticleMaterial - 设置粒子材质
111. particles/animation/animateSize - 动画粒子大小
112. particles/animation/animateColor - 动画粒子颜色
113. terrain/generation/createTerrain - 创建地形
114. terrain/generation/generateHeightmap - 生成高度图
115. terrain/generation/applyNoise - 应用噪声
116. terrain/texture/setTerrainTexture - 设置地形纹理
117. terrain/texture/blendTextures - 混合纹理
118. terrain/lod/enableTerrainLOD - 启用地形LOD
119. terrain/collision/enableTerrainCollision - 启用地形碰撞
120. water/system/createWaterSurface - 创建水面
121. water/waves/addWaves - 添加波浪
122. water/reflection/enableReflection - 启用水面反射
123. water/refraction/enableRefraction - 启用水面折射
124. vegetation/system/createVegetation - 创建植被
125. vegetation/grass/addGrass - 添加草地
126. vegetation/trees/addTrees - 添加树木
127. weather/system/createWeatherSystem - 创建天气系统
128. weather/rain/enableRain - 启用雨效
129. weather/snow/enableSnow - 启用雪效
130. weather/wind/setWindDirection - 设置风向
131. weather/wind/setWindStrength - 设置风力
132. environment/time/setTimeOfDay - 设置时间
133. editor/project/createProject - 创建项目
134. editor/project/openProject - 打开项目
135. editor/project/saveProject - 保存项目
136. editor/project/closeProject - 关闭项目
137. editor/project/exportProject - 导出项目
138. editor/project/importProject - 导入项目
139. editor/project/setProjectSettings - 设置项目配置
140. editor/project/getProjectInfo - 获取项目信息
141. editor/asset/importAsset - 导入资产
142. editor/asset/deleteAsset - 删除资产
143. editor/asset/renameAsset - 重命名资产
144. editor/asset/moveAsset - 移动资产
145. editor/asset/createFolder - 创建文件夹
146. editor/asset/getAssetInfo - 获取资产信息
147. editor/asset/generateThumbnail - 生成缩略图
148. editor/scene/createEntity - 创建实体
149. editor/scene/deleteEntity - 删除实体
150. editor/scene/selectEntity - 选择实体
151. editor/scene/duplicateEntity - 复制实体
152. editor/scene/groupEntities - 组合实体
153. editor/scene/ungroupEntities - 取消组合
154. editor/scene/setEntityParent - 设置父对象
155. editor/scene/moveEntity - 移动实体
156. editor/scene/rotateEntity - 旋转实体
157. editor/scene/scaleEntity - 缩放实体
158. editor/scene/hideEntity - 隐藏实体
159. editor/scene/showEntity - 显示实体
160. editor/scene/lockEntity - 锁定实体
161. editor/scene/unlockEntity - 解锁实体
162. editor/scene/focusOnEntity - 聚焦实体
163. editor/ui/createUIElement - 创建UI元素
164. editor/ui/deleteUIElement - 删除UI元素
165. editor/ui/setUIPosition - 设置UI位置
166. editor/ui/setUISize - 设置UI大小
167. editor/ui/setUIText - 设置UI文本
168. editor/ui/setUIColor - 设置UI颜色
169. editor/ui/setUIFont - 设置UI字体
170. editor/ui/setUIImage - 设置UI图像
171. editor/ui/addUIEvent - 添加UI事件
172. editor/ui/removeUIEvent - 移除UI事件
173. editor/ui/setUIVisible - 设置UI可见性
174. editor/ui/setUIEnabled - 设置UI启用状态
175. editor/ui/setUILayer - 设置UI层级
176. editor/ui/alignUIElements - 对齐UI元素
177. editor/ui/distributeUIElements - 分布UI元素
178. editor/tools/enableGizmo - 启用操作手柄
179. editor/tools/setGizmoMode - 设置手柄模式
180. editor/tools/enableGrid - 启用网格
181. editor/tools/setGridSize - 设置网格大小
182. editor/tools/enableSnap - 启用吸附
183. server/user/registerUser - 用户注册
184. server/user/loginUser - 用户登录
185. server/user/logoutUser - 用户登出
186. server/user/updateUserProfile - 更新用户资料
187. server/user/changePassword - 修改密码
188. server/user/resetPassword - 重置密码
189. server/user/getUserInfo - 获取用户信息
190. server/user/deleteUser - 删除用户
191. server/user/setUserRole - 设置用户角色
192. server/user/validateToken - 验证令牌
193. server/project/createProject - 创建服务器项目
194. server/project/deleteProject - 删除服务器项目
195. server/project/updateProject - 更新项目信息
196. server/project/getProjectList - 获取项目列表
197. server/project/getProjectDetails - 获取项目详情
198. server/project/shareProject - 分享项目
199. server/project/unshareProject - 取消分享
200. server/project/setProjectPermission - 设置项目权限
201. server/project/forkProject - 复制项目
202. server/project/archiveProject - 归档项目
203. server/project/restoreProject - 恢复项目
204. server/project/exportProjectData - 导出项目数据
205. server/project/importProjectData - 导入项目数据
206. server/project/getProjectStats - 获取项目统计
207. server/project/backupProject - 备份项目
208. server/asset/uploadAsset - 上传资产
209. server/asset/downloadAsset - 下载资产
210. server/asset/deleteAsset - 删除服务器资产
211. server/asset/getAssetList - 获取资产列表
212. server/asset/getAssetInfo - 获取资产信息
213. server/asset/updateAssetInfo - 更新资产信息
214. server/asset/moveAssetToFolder - 移动资产到文件夹
215. server/asset/createAssetFolder - 创建资产文件夹
216. server/asset/deleteAssetFolder - 删除资产文件夹
217. server/asset/shareAsset - 分享资产
218. server/asset/getAssetVersions - 获取资产版本
219. server/asset/createAssetVersion - 创建资产版本
220. server/asset/restoreAssetVersion - 恢复资产版本
221. server/asset/generateAssetThumbnail - 生成资产缩略图
222. server/asset/optimizeAsset - 优化资产
223. server/collaboration/joinRoom - 加入协作房间
224. server/collaboration/leaveRoom - 离开协作房间
225. server/collaboration/sendOperation - 发送协作操作
226. server/collaboration/receiveOperation - 接收协作操作
227. server/collaboration/resolveConflict - 解决编辑冲突
228. server/collaboration/getOnlineUsers - 获取在线用户
229. server/collaboration/broadcastMessage - 广播消息
230. server/collaboration/lockResource - 锁定资源
231. server/collaboration/unlockResource - 解锁资源
232. server/collaboration/syncState - 同步状态

## 📈 建议

1. **完善双重注册**：确保所有节点既在引擎注册又在编辑器集成
2. **统一节点管理**：建立统一的节点注册机制
3. **定期同步检查**：定期验证引擎和编辑器的节点一致性

---
*报告生成时间：2025/7/11 16:35:58*
